import { useState, useEffect } from 'react';

import { databaseService } from '../services/DatabaseService';
import { Business, BusinessInput } from '../types/Business';
import { Article, ArticleInput } from '../types/Article';

export const useBusinesses = () => {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const subscription = databaseService.getAllBusinesses().subscribe({
      next: data => {
        setBusinesses(data);
        setLoading(false);
        setError(null);
      },
      error: err => {
        console.error('Error fetching businesses:', err);
        setError('Failed to load businesses');
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, []);

  const createBusiness = async (input: BusinessInput): Promise<Business> => {
    try {
      setError(null);
      return await databaseService.createBusiness(input);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to create business';
      setError(errorMessage);
      throw err;
    }
  };

  const updateBusiness = async (
    id: string,
    input: BusinessInput,
  ): Promise<Business> => {
    try {
      setError(null);
      return await databaseService.updateBusiness(id, input);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update business';
      setError(errorMessage);
      throw err;
    }
  };

  const deleteBusiness = async (id: string): Promise<void> => {
    try {
      setError(null);
      await databaseService.deleteBusiness(id);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete business';
      setError(errorMessage);
      throw err;
    }
  };

  return {
    businesses,
    loading,
    error,
    createBusiness,
    updateBusiness,
    deleteBusiness,
  };
};

export const useBusiness = (id: string | null) => {
  const [business, setBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setBusiness(null);
      setLoading(false);
      return;
    }

    const subscription = databaseService.getBusiness(id).subscribe({
      next: data => {
        setBusiness(data);
        setLoading(false);
        setError(null);
      },
      error: err => {
        console.error('Error fetching business:', err);
        setError('Failed to load business');
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, [id]);

  return { business, loading, error };
};

export const useArticles = (businessId?: string) => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const subscription = businessId
      ? databaseService.getArticlesByBusiness(businessId)
      : databaseService.getAllArticles();

    const sub = subscription.subscribe({
      next: data => {
        setArticles(data);
        setLoading(false);
        setError(null);
      },
      error: err => {
        console.error('Error fetching articles:', err);
        setError('Failed to load articles');
        setLoading(false);
      },
    });

    return () => sub.unsubscribe();
  }, [businessId]);

  const createArticle = async (input: ArticleInput): Promise<Article> => {
    try {
      setError(null);
      return await databaseService.createArticle(input);
    } catch (err) {
      console.log(err, 'eeeeeeeeeeeeeeeeee');
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to create article';
      setError(errorMessage);
      throw err;
    }
  };

  const updateArticle = async (
    id: string,
    input: ArticleInput,
  ): Promise<Article> => {
    try {
      setError(null);
      return await databaseService.updateArticle(id, input);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update article';
      setError(errorMessage);
      throw err;
    }
  };

  const deleteArticle = async (id: string): Promise<void> => {
    try {
      setError(null);
      await databaseService.deleteArticle(id);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete article';
      setError(errorMessage);
      throw err;
    }
  };

  return {
    articles,
    loading,
    error,
    createArticle,
    updateArticle,
    deleteArticle,
  };
};

export const useArticle = (id: string | null) => {
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setArticle(null);
      setLoading(false);
      return;
    }

    const subscription = databaseService.getArticle(id).subscribe({
      next: data => {
        setArticle(data);
        setLoading(false);
        setError(null);
      },
      error: err => {
        console.error('Error fetching article:', err);
        setError('Failed to load article');
        setLoading(false);
      },
    });

    return () => subscription.unsubscribe();
  }, [id]);

  return { article, loading, error };
};
