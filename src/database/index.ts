import { BehaviorSubject, Observable } from 'rxjs';
import { BusinessDocument } from '../types/Business';
import { ArticleDocument } from '../types/Article';

// Database state type
interface DatabaseState {
  businesses$: BehaviorSubject<BusinessDocument[]>;
  articles$: BehaviorSubject<ArticleDocument[]>;
}

// Database instance storage
let databaseState: DatabaseState | null = null;

// Helper function to ensure database is initialized
const ensureDatabaseInitialized = (): DatabaseState => {
  if (!databaseState) {
    throw new Error(
      'Database not initialized. Call initializeDatabase() first.',
    );
  }
  return databaseState;
};

// Database initialization
export const initializeDatabase = async (): Promise<void> => {
  if (databaseState) {
    return;
  }

  try {
    console.log('Initializing mock database...');
    databaseState = {
      businesses$: new BehaviorSubject<BusinessDocument[]>([]),
      articles$: new BehaviorSubject<ArticleDocument[]>([]),
    };
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

export const closeDatabase = async (): Promise<void> => {
  if (databaseState) {
    databaseState.businesses$.complete();
    databaseState.articles$.complete();
    databaseState = null;
  }
};

// Business operations
export const insertBusiness = async (
  business: BusinessDocument,
): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.businesses$.value;
  state.businesses$.next([...current, business]);
};

export const updateBusiness = async (
  id: string,
  updates: Partial<BusinessDocument>,
): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.businesses$.value;
  const index = current.findIndex(b => b.id === id);
  if (index !== -1) {
    current[index] = { ...current[index], ...updates };
    state.businesses$.next([...current]);
  }
};

export const deleteBusiness = async (id: string): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.businesses$.value;
  state.businesses$.next(current.filter(b => b.id !== id));
};

export const findBusiness = (
  id: string,
): Observable<BusinessDocument | null> => {
  const state = ensureDatabaseInitialized();
  return new Observable(subscriber => {
    const subscription = state.businesses$.subscribe(businesses => {
      const business = businesses.find(b => b.id === id) || null;
      subscriber.next(business);
    });
    return () => subscription.unsubscribe();
  });
};

export const findAllBusinesses = (): Observable<BusinessDocument[]> => {
  const state = ensureDatabaseInitialized();
  return state.businesses$.asObservable();
};

// Article operations
export const insertArticle = async (
  article: ArticleDocument,
): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.articles$.value;
  state.articles$.next([...current, article]);
};

export const updateArticle = async (
  id: string,
  updates: Partial<ArticleDocument>,
): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.articles$.value;
  const index = current.findIndex(a => a.id === id);
  if (index !== -1) {
    current[index] = { ...current[index], ...updates };
    state.articles$.next([...current]);
  }
};

export const deleteArticle = async (id: string): Promise<void> => {
  const state = ensureDatabaseInitialized();
  const current = state.articles$.value;
  state.articles$.next(current.filter(a => a.id !== id));
};

export const findArticle = (id: string): Observable<ArticleDocument | null> => {
  const state = ensureDatabaseInitialized();
  return new Observable(subscriber => {
    const subscription = state.articles$.subscribe(articles => {
      const article = articles.find(a => a.id === id) || null;
      subscriber.next(article);
    });
    return () => subscription.unsubscribe();
  });
};

export const findAllArticles = (): Observable<ArticleDocument[]> => {
  const state = ensureDatabaseInitialized();
  return state.articles$.asObservable();
};

export const findArticlesByBusiness = (
  businessId: string,
): Observable<ArticleDocument[]> => {
  const state = ensureDatabaseInitialized();
  return new Observable(subscriber => {
    const subscription = state.articles$.subscribe(articles => {
      const filtered = articles.filter(a => a.business_id === businessId);
      subscriber.next(filtered);
    });
    return () => subscription.unsubscribe();
  });
};

// Legacy compatibility - object interface similar to the original class
export const createDatabaseInterface = () => ({
  insertBusiness,
  updateBusiness,
  deleteBusiness,
  findBusiness,
  findAllBusinesses,
  insertArticle,
  updateArticle,
  deleteArticle,
  findArticle,
  findAllArticles,
  findArticlesByBusiness,
});

// For backwards compatibility with existing code that expects getDatabase()
export const getDatabase = () => {
  ensureDatabaseInitialized();
  return createDatabaseInterface();
};

// Alternative: Export all database operations as a single object
export const database = {
  initialize: initializeDatabase,
  close: closeDatabase,
  business: {
    insert: insertBusiness,
    update: updateBusiness,
    delete: deleteBusiness,
    findOne: findBusiness,
    findAll: findAllBusinesses,
  },
  article: {
    insert: insertArticle,
    update: updateArticle,
    delete: deleteArticle,
    findOne: findArticle,
    findAll: findAllArticles,
    findByBusiness: findArticlesByBusiness,
  },
};
